name: Issues
on:
  issues:
    types: [opened, edited, reopened, closed]

jobs:
  handle-issue:
    name: Handle issue event
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Issue Event Handler
        run: |
          echo "Issue event triggered: ${{ github.event.action }}"
          echo "Issue number: ${{ github.event.issue.number }}"
          echo "Issue title: ${{ github.event.issue.title }}"
          echo "Issue author: ${{ github.event.issue.user.login }}"
          echo "Issue URL: ${{ github.event.issue.html_url }}"
